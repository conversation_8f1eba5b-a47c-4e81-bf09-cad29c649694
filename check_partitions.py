#!/usr/bin/env python3
# coding: utf-8
"""
检查数据库中的分区情况
"""

from sqlalchemy import create_engine, text
from config import PG_URL

def check_weather_alarm_partitions():
    """检查weather_alarm表的分区情况"""
    engine = create_engine(PG_URL)
    
    try:
        with engine.begin() as conn:
            # 查询所有weather_alarm相关的分区
            result = conn.execute(text("""
                SELECT schemaname, tablename, 
                       pg_get_expr(c.relpartbound, c.oid) as partition_bounds
                FROM pg_tables pt
                JOIN pg_class c ON c.relname = pt.tablename
                WHERE pt.tablename LIKE 'weather_alarm_p%'
                ORDER BY pt.tablename;
            """))
            
            print("现有的weather_alarm分区:")
            for row in result:
                print(f"  {row.schemaname}.{row.tablename}: {row.partition_bounds}")
                
            # 查询分区的详细范围
            result2 = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_get_expr(c.relpartbound, c.oid) as partition_bounds
                FROM pg_tables pt
                JOIN pg_class c ON c.relname = pt.tablename
                WHERE pt.tablename LIKE 'weather_alarm_p%'
                ORDER BY pt.tablename;
            """))
            
            print("\n分区详细信息:")
            for row in result2:
                print(f"  {row.tablename}: {row.partition_bounds}")
                
    except Exception as e:
        print(f"检查分区时发生错误: {e}")

if __name__ == "__main__":
    check_weather_alarm_partitions()
